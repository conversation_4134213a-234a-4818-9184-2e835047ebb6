from py2neo import Graph, Node, Relationship
import re
import PyPDF2
#import fitz  # PyMuPDF的别名

def pdf_to_string_pypdf2(pdf_path):
    text = ""
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        for page in reader.pages:
            text += page.extract_text() or ""  # 避免None值
    return text.strip()


# def pdf_to_string_pymupdf(pdf_path):
#     text = ""
#     doc = fitz.open(pdf_path)
#     for page in doc:
#         text += page.get_text()
#     return text.strip()


# 连接到Neo4j数据库
graph = Graph("neo4j://localhost:7687", auth=("neo4j", "Oracle123"))

def extract_recipe_info(text):
    # 分割菜系
    cuisines = {}
    current_cuisine = None
    current_category = None
    current_method = None

    # 用于存储所有解析的菜品信息
    recipes = []

    lines = text.split('\n')
    current_recipe = {}

    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 检测是否包含"Cuisine"单词
        if "Cuisine" in line:
            continue

        # 处理其他属性行
        if ':' in line or '：' in line:
            key, value = re.split('[：:]', line, 1)
            key = key.strip()
            value = value.strip()

            # 检测菜系
            #if key in ['川菜', '鲁菜', '粤菜', '苏菜', '浙菜', '湘菜', '闽菜', '徽菜', '北京菜', '东北菜']:
            if key == '菜系':
                current_cuisine = value
                continue

            # 检测分类（热菜/凉菜）
            if value in ['热菜', '凉菜']:
                current_category = value
                continue
                

            if key =='食材':
                # 将食材字符串分割成列表
                ingredients = [i.strip() for i in value.replace('、', ',').split(',')]
                current_recipe[key] = ingredients
            else:
                current_recipe[key] = value

            continue

        elif line and line[0].isdigit():
            if current_method is None or len(current_method)== 0:
                current_method = line.join(';')
            else:
                current_method.join(line)
            continue

        # 检测新菜品的开始（不以“：”结尾的行）
        if ':' not in line and '：' not in line:
            # 保存前一个菜品
            if current_recipe:
                recipes.append(current_recipe)

            # 开始新菜品
            current_recipe = {
                '菜名': line,
                '菜系': current_cuisine,
                '类别': current_category,
                '方法': current_method
            }
            continue

            

    # 添加最后一个菜品
    if current_recipe:
        recipes.append(current_recipe)

    return recipes

def create_knowledge_graph(recipes):
    #清空数据库
    graph.run("MATCH (n) DETACH DELETE n")

    # 用于存储已创建的节点
    cuisine_nodes = {}
    category_nodes = {}
    ingredient_nodes = {}
    taste_nodes = {}

    for recipe in recipes:
        # 创建菜系节点
        if recipe['菜系'] not in cuisine_nodes:
            cuisine_node = Node("Cuisine", name=recipe['菜系'])
            graph.create(cuisine_node)
            cuisine_nodes[recipe['菜系']] = cuisine_node

        # 创建分类节点
        if recipe['类别'] not in category_nodes:
            category_node = Node("Category", name=recipe['类别'])
            graph.create(category_node)
            category_nodes[recipe['类别']] = category_node

        # 创建菜名节点
        recipe_node = Node("Recipe",
                           name=recipe['菜名'],
                           cooking_process=recipe.get('烹饪过程', ''),
                           taste_description=recipe.get('食用感受', ''))
        graph.create(recipe_node)

        # 创建食材节点和关系
        food_key = '食材' if '食材' in recipe else '食材'
        if food_key in recipe:
            for ingredient in recipe[food_key]:
                if ingredient not in ingredient_nodes:
                    ingredient_node = Node("Ingredient", name=ingredient)
                    graph.create(ingredient_node)
                    ingredient_nodes[ingredient] = ingredient_node

                # 创建菜品和食材的关系
                graph.create(Relationship(recipe_node, "CONTAINS", ingredient_node))

        # 创建口味节点和关系
        if '口味' in recipe:
            tastes = recipe['口味'].split(',')
            for taste in tastes:
                if taste not in taste_nodes:
                    taste_node = Node("Taste", name=taste)
                    graph.create(taste_node)
                    taste_nodes[taste] = taste_node

                # 创建菜品与口味关系
                graph.create(Relationship(recipe_node, "HAS_TASTE", taste_nodes[taste]))

        # 创建菜品与菜系关系
        graph.create(Relationship(recipe_node, "BELONGS_TO", cuisine_nodes[recipe['菜系']]))

        # 创建菜品与分类关系
        graph.create(Relationship(recipe_node, "CATEGORIZED_AS", category_nodes[recipe['类别']]))

def query_by_cuisine(cuisine_name):
    query = """
    MATCH (r:Recipe)-[:BELONGS_TO]->(c:Cuisine {name: $cuisine})
    RETURN r.name as name, r.cooking_process as process, r.taste_desc
    """
    return graph.run(query, cuisine=cuisine_name).data()

def query_by_ingredient(ingredient_name):
    query = """
    MATCH (r:Recipe)-[:CONTAINS]->(i:Ingredient)
    WHERE i.name CONTAINS $ingredient
    RETURN r.name as name, r.cooking_process as process, r.taste_desc
    """
    return graph.run(query, ingredient=ingredient_name).data()

def query_by_taste(taste):
    query = """
    MATCH (r:Recipe)-[:HAS_TASTE]->(t:Taste)
    WHERE t.name CONTAINS $taste
    RETURN r.name as name, r.cooking_process as process, r.taste_desc
    """
    return graph.run(query, taste=taste).data()

def print_recipe_details(result):
    print("-" * 50)
    print(f"菜名: {result['name']}")
    print(f"烹饪过程: {result['process']}")
    print(f"味道描述: {result['taste']}")
    print('=' * 50)

def test_queries():
    print("测试查询功能:")
    # 测试按菜系查询
    for cuisine in ["川菜", "鲁菜", "粤菜"]:
        print(f"\n按菜系查询({cuisine}):")
        results = query_by_cuisine(cuisine)
        for result in results:
            print_recipe_details(result)

    # 测试按食材查询
    print("\n按食材查询(豆角):")
    results = query_by_ingredient("豆角")
    for result in results:
        print_recipe_details(result)

    # 测试按口味查询
    print("\n按口味查询(辣):")
    results = query_by_taste("辣")
    for result in results:
        print_recipe_details(result)



# 示例调用
result = pdf_to_string_pypdf2("D:\\Datasets\\烹饪\\菜谱大全.pdf")
print(result)
recipes = extract_recipe_info(result)
create_knowledge_graph(recipes)
