# train.py
from transformers import AutoModelForCausalLM, AutoTokenizer, TrainingArguments, Trainer, DataCollatorForLanguageModeling
from transformers import BitsAndBytesConfig
from transformers import AutoTokenizer
from peft import LoraConfig, get_peft_model, TaskType
from data_loader import get_dataset
from torch.backends.cuda import sdp_kernel
from torch.utils.tensorboard import SummaryWriter  # ✅ 添加 SummaryWriter
import torch
# for TensorBoard logging
from transformers import TrainerCallback
class TensorBoardLoggingCallback(TrainerCallback):
    def __init__(self, writer):
        super().__init__()
        self.writer = writer

    def on_log(self, args, state, control, logs=None, **kwargs):
        if logs is None:
            return
        step = state.global_step
        for k, v in logs.items():
            if isinstance(v, (int, float)):
                self.writer.add_scalar(k, v, step)

    def on_train_end(self, args, state, control, **kwargs):
        self.writer.flush()
        self.writer.close()
# end for tensorboard logging

# 启用 flash attention
torch.backends.cuda.enable_flash_sdp(True)
torch.backends.cuda.enable_mem_efficient_sdp(True)
torch.backends.cuda.enable_math_sdp(False)

print(torch.cuda.get_device_name(0))
print("当前使用 attention 实现：", sdp_kernel()) # Pytorch <= 2.3 使用
print("Torch version:", torch.__version__)
print("CUDA available:", torch.cuda.is_available())
print("SDPA backend:", torch.backends.cuda.can_use_flash_attention) # PyTorch < 2.5.0 不支持torch.backends.cuda.preferred_sdpa_backend，请注释掉本行
print(torch.backends.cuda.flash_sdp_enabled())


#model_name = "Qwen/Qwen3-0.6B"
#model_name = "D:\\Models-Safetensor\\Qwen1.5-0.5B"
#model_name = "D:\\Models-Safetensor\\Qwen2.5-0.5B"
model_name = "D:\\Models-Safetensor\\Qwen3-0.6B"
output_dir = "./output"
log_dir = output_dir + "/log"

writer = SummaryWriter(log_dir=log_dir)  # ✅ 初始化 SummaryWriter

def tokenize_example(example):
    input_text = example["prompt"]
    target_text = example["response"]

    # 拼接方式（推荐 Qwen3 的做法）
    full_text = input_text + target_text

    tokenized = tokenizer(
        full_text,
        truncation=True,
        padding="max_length",
        max_length=512
    )

    # 设置 labels，只监督 response 部分（不含 prompt）
    target_tokenized = tokenizer(
        target_text,
        truncation=True,
        padding="max_length",
        max_length=512
    )

    tokenized["labels"] = target_tokenized["input_ids"]
    return tokenized


# 加载模型 & tokenizer（量化）
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)

# 自动量化
# model = AutoModelForCausalLM.from_pretrained(
#     model_name,
#     load_in_4bit=True,
#     device_map="auto",
#     trust_remote_code=True
# )

# 手动量化
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_quant_type="nf4",
    llm_int8_threshold=6.0,
    llm_int8_has_fp16_weight=False,
)

model = AutoModelForCausalLM.from_pretrained(
    model_name,
    quantization_config=bnb_config,
    device_map="auto",
    trust_remote_code=True
)

# 下面要用到模型结构，所以需要查看模型结构，用来验证模型结构填写的是否正确。
for name, module in model.named_modules():
    print("target_modules", name)
    
for name, module in model.named_modules():
    if 'attn' in name.lower():
        print("target_modules_att", name)
    
# LoRA 配置
lora_config = LoraConfig(
    r=8,
    lora_alpha=16,
    target_modules=["q_proj", "v_proj"], # target_modules 指的是你要注入 LoRA 的目标模块名称：如果你用的是 Qwen 系列模型，通常是：["q_proj", "v_proj"]， qwen3Qwen3 使用的是 c_attn,它合并了 q, k, v 的多头注意力; 如果是 LLaMA 系列模型，可能是：["q_proj", "v_proj", "k_proj", "o_proj"]。
    lora_dropout=0.05,
    bias="none",
    task_type=TaskType.CAUSAL_LM
)
model = get_peft_model(model, lora_config)

# 数据集
dataset = get_dataset("trainDatasets.jsonl")
#tokenized_dataset = dataset.map(lambda e: tokenizer(e["text"], truncation=True, padding="max_length", max_length=512), batched=True)
tokenized_dataset = dataset.map(tokenize_example, remove_columns=dataset.column_names)
data_collator = DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False)

# 训练参数
training_args = TrainingArguments(
    output_dir=output_dir,
    num_train_epochs=20,
    per_device_train_batch_size=4,
    gradient_accumulation_steps=4,
    optim="paged_adamw_8bit",
    logging_steps=10, # 训练时每 10 个 step 保存一次
    save_strategy="epoch",
    learning_rate=2e-4,
    bf16=False,
    fp16=True,
    remove_unused_columns=False,
    report_to="none"
)

# Trainer 启动
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=tokenized_dataset,
    data_collator=data_collator,
    callbacks=[TensorBoardLoggingCallback(writer)]  # ✅ 添加回调
)

# 加载模型结构图到 TensorBoard
try:
    dummy_input = tokenizer("你好", return_tensors="pt").to(model.device)
    writer.add_graph(model, (dummy_input['input_ids'],))
except Exception as e:
    print("模型结构图添加失败:", e)

trainer.train()
model.save_pretrained(output_dir)
tokenizer.save_pretrained(output_dir)

# 快速排序