
from torch.nn.functional import scaled_dot_product_attention
from torch.nn.attention import SDPBackend, sdpa_kernel

import torch
q = torch.randn(1, 8, 128, 64, dtype=torch.float16, device='cuda')  # 查詢
k = torch.randn(1, 8, 128, 64, dtype=torch.float16, device='cuda')  # 鍵
v = torch.randn(1, 8, 128, 64, dtype=torch.float16, device='cuda')  # 值
with torch.backends.cuda.sdp_kernel(enable_flash=True, enable_math=False, enable_mem_efficient=False):
    out = torch.nn.functional.scaled_dot_product_attention(q, k, v)
    if out is not None:
        print("Flash Attention used successfully!")
    else:
         print( "Failed to use Flash Attention")

    # Only enable flash attention backend
    with sdpa_kernel(SDPBackend.FLASH_ATTENTION):
        scaled_dot_product_attention(...)

    # Enable the Math or Efficient attention backends
    with sdpa_kernel([SDPBackend.MATH, SDPBackend.EFFICIENT_ATTENTION]):
        scaled_dot_product_attention(...)

with torch.nn.attention.sdpa_kernel(SDPBackend.SDPA_KERNEL_FLASH):
    out = torch.nn.functional.scaled_dot_product_attention(q, k, v)
    if out is not None:
        print("Flash Attention used successfully!")
    else:
         print( "Failed to use Flash Attention")