# 初始化模型和 tokenizer（你已有 tokenizer）
from transformers import AutoTokenizer

tokenizer = AutoTokenizer.from_pretrained("../model/")
config = MiniMindConfig(hidden_size=512, num_hidden_layers=6)
model = MiniMindForCausalLM(config)

# 加载你训练的权重
state_dict = torch.load("pretrain_512.pth", map_location="cpu")
model.load_state_dict(state_dict)

# 保存为 HuggingFace 可用结构
model.save_pretrained("./hf_minimind")
tokenizer.save_pretrained("./hf_minimind")