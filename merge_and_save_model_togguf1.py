import os
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import subprocess

# === 配置区 ===
#base_model_path = "Qwen/Qwen3-0.6B"           # 原始 HuggingFace 模型（或本地路径）
#base_model_path = "D:/Models-Safetensor/Qwen1.5-0.5B"
#base_model_path = "D:/Models-Safetensor/Qwen2.5-0.5B"
base_model_path = "D:/Models-Safetensor/Qwen3-0.6B"
lora_model_path = "./output"                  # LoRA adapter 训练输出目录
merged_model_path = "./merged_model"          # 合并后模型保存目录
# gguf_output_file = "qwen3-06b-fp16.gguf"      # GGUF 文件名（最终输出）
#gguf_output_file = "metalstech-1.5b-chat.gguf"
gguf_output_file = "metalstech-chat.gguf"
llama_cpp_convert_path = "D:/Work/llama.cpp/convert_hf_to_gguf.py"  # convert.py 文件路径

# === 第一步：加载并合并 LoRA 权重 ===
print("🔄 合并 LoRA adapter 到 base model...")
model = AutoModelForCausalLM.from_pretrained(base_model_path, torch_dtype="auto")
model = PeftModel.from_pretrained(model, lora_model_path)
model = model.merge_and_unload()
model.save_pretrained(merged_model_path)

# === 第二步：保存 tokenizer ===
print("💾 保存 tokenizer...")
tokenizer = AutoTokenizer.from_pretrained(base_model_path)
tokenizer.save_pretrained(merged_model_path)

# === 第三步：调用 convert.py 导出 GGUF ===
print("🚀 调用 convert.py 导出 GGUF 格式...")
command = [
    "python", llama_cpp_convert_path,
    "--outfile", gguf_output_file,
    "--outtype", "f16",
    merged_model_path
]

# 也可以直接使用python命令导出gguf
# python -m transformers.convert --model_name_or_path merged_model --ggml

result = subprocess.run(command, shell=True)

if result.returncode == 0:
    print(f"✅ GGUF 模型已成功导出为: {gguf_output_file}")
else:
    print("❌ GGUF 导出失败，请检查错误信息。")