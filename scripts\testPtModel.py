import sys
from pathlib import Path

# 添加父目录到 Python 模块搜索路径
parent_dir = Path(__file__).parent.parent
sys.path.append(str(parent_dir))

import torch
from transformers import AutoTokenizer
from model.model_minimind import MiniMindConfig, MiniMindForCausalLM

pth_path = "../out/pretrain_512.pth"  # .pth 文件路径
model_path = "../model/"
# 加载 tokenizer
tokenizer = AutoTokenizer.from_pretrained(model_path)

# 加载模型结构与权重
config = MiniMindConfig(hidden_size=512, num_hidden_layers=8)
model = MiniMindForCausalLM(config)
model.load_state_dict(torch.load(pth_path, map_location="cpu"))
model.eval()

# 推理
prompt = "你好"
input_ids = tokenizer(prompt, return_tensors="pt").input_ids
with torch.no_grad():
    out = model(input_ids)
    logits = out["logits"]
    next_token_id = torch.argmax(logits[:, -1, :], dim=-1)
    next_token = tokenizer.decode(next_token_id)

print("模型生成：", prompt + next_token)