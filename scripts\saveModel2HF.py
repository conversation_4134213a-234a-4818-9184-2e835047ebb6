import sys
from pathlib import Path

# 兼容 __file__ 在调试器/Run Code 中可能为未定义的问题
# only for debugging from vscode ide
# try:
#     base_path = Path(__file__).resolve()
# except NameError:
#     # VS Code "Run Code" 或调试时 __file__ 可能不存在，手动指定一个默认路径
#     base_path = Path(sys.argv[0]).resolve() if sys.argv[0] else Path.cwd() / "saveModel2HF.py"
#     parent_dir = base_path.parent.parent
#     sys.path.append(str(parent_dir))

# 添加父目录到 Python 模块搜索路径
parent_dir = Path(__file__).parent.parent
sys.path.append(str(parent_dir))

# 初始化模型和 tokenizer（你已有 tokenizer）
import torch
from transformers import AutoTokenizer
from model.model_minimind import MiniMindConfig, MiniMindForCausalLM

pth_path = "../out/pretrain_512.pth"  # .pth 文件路径

tokenizer = AutoTokenizer.from_pretrained("../model/")
config = MiniMindConfig(hidden_size=512, num_hidden_layers=8)
model = MiniMindForCausalLM(config)

# 加载你训练的权重
state_dict = torch.load(pth_path, map_location="cpu")
model.load_state_dict(state_dict)

# 保存为 HuggingFace 可用结构
# class MiniMindForCausalLM(PreTrainedModel, GenerationMixin):
    #...
    # def __init__(self, config: MiniMindConfig = None):
        #self.lm_head = nn.Linear(self.config.hidden_size, self.config.vocab_size, bias=False)
        #self.model.embed_tokens.weight = self.lm_head.weight // 这句话导致 embed_tokens.weight 和 lm_head.weight共享张量，这个是 Hugging Face 的标准不运行的，所以在下面的save_pretrained()中将safe_serialization=False，这样就不保存成safetensor的格式，而是bin的格式
model.save_pretrained("./hf_minimind", safe_serialization=False)
tokenizer.save_pretrained("./hf_minimind")