import tensorflow as tf
from tensorflow.keras import layers
from tensorflow.keras.layers import Dense
from tensorflow.keras.models import Sequential
import tensorflow_datasets as tfds

# step 2: 自动加载IMDB数据（训练+测试），包含 label 和 text
(train_ds, val_ds), ds_info = tfds.load(
    'imdb_reviews',
    split=['train[:80%]', 'train[80%:]'],
    shuffle_files=True,
    as_supervised=True,  # 返回 (text, label)
    with_info=True
)

# step 3: 文本向量化 TextVectorization, 把原始文本变成整数序列
# 设置参数
vocab_size = 10000  # 最大词汇表长度
sequence_length = 250  # 每个样本最多250个单词

# 向量化层
vectorize_layer = layers.TextVectorization(
    max_tokens=vocab_size,
    output_mode='int',
    output_sequence_length=sequence_length
)

# 使用训练数据构建词汇表
train_text = train_ds.map(lambda text, label: text)
vectorize_layer.adapt(train_text)
print(vectorize_layer.get_vocabulary()[:20]) # 打印前20个单词, 它们出现次数最多的单词

# step 4: 训练向量化层, 构建模型
embedding_dim = 16

model = tf.keras.Sequential([
    vectorize_layer,
    layers.Embedding(input_dim=vocab_size, output_dim=embedding_dim, name="embedding"),
    layers.GlobalAveragePooling1D(),
    layers.Dense(16, activation='relu'),
    layers.Dense(1, activation='sigmoid')
])

# step 5: 编译和训练模型
model.compile(optimizer='adam',
              loss='binary_crossentropy',
              metrics=['accuracy'])

tensorboard_callback = tf.keras.callbacks.TensorBoard(log_dir='logs', histogram_freq=1)

model.fit(train_ds.batch(32),
          validation_data=val_ds.batch(32),
          epochs=10,
          callbacks=[tensorboard_callback])

# step 6: 导出嵌入向量并可视化
# 获取嵌入层的权重
embedding_layer = model.get_layer(name="embedding")
embedding_weights = embedding_layer.get_weights()[0]

# 获取词汇表
vocab = vectorize_layer.get_vocabulary()

# 写入 TSV 文件（词，向量）
with open("vectors.tsv", "w", encoding='utf-8') as f_vectors, open("metadata.tsv", "w", encoding='utf-8') as f_metadata:
    for i in range(1, len(vocab)):  # 跳过 [PAD] token
        word = vocab[i]
        vec = embedding_weights[i]
        f_metadata.write(word + "\n")
        f_vectors.write("\t".join([str(x) for x in vec]) + "\n")