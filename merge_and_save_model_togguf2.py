import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel
from transformers.utils import is_safetensors_available
import subprocess

# 配置参数
base_model_path = "Qwen/Qwen3-0.6B"           # HF 格式的基础模型路径或名称
lora_model_path = "./output"                  # 你的 LoRA adapter 目录
merged_model_path = "./merged_model"          # 合并后输出目录
gguf_output_name = "qwen3-0.6b-merged.gguf"    # 转换后模型名

# Step 1: 加载基础模型和LoRA权重
print("🧠 加载基础模型和LoRA权重...")
base_model = AutoModelForCausalLM.from_pretrained(
    base_model_path,
    torch_dtype=torch.float16,
    device_map="auto"
)
model = PeftModel.from_pretrained(base_model, lora_model_path)

# Step 2: 合并权重
print("🔗 合并LoRA参数进基础模型...")
model = model.merge_and_unload()

# Step 3: 保存合并后的模型
print("💾 保存合并后的模型到:", merged_model_path)
model.save_pretrained(merged_model_path, safe_serialization=True)
tokenizer = AutoTokenizer.from_pretrained(base_model_path)
tokenizer.save_pretrained(merged_model_path)

# Step 4: 转换为 GGUF
print("🛠️ 开始转换为 GGUF 格式...")
convert_script = os.path.join(
    os.path.dirname(__file__),
    "transformers",
    "convert.py"
)

# 尝试执行转换脚本（假设 transformers 已安装）
subprocess.run([
    "python",
    "-m", "transformers.convert",
    "--model_name_or_path", merged_model_path,
    "--ggml",
    "--outfile", gguf_output_name
])
print(f"✅ 转换完成！生成的 GGUF 模型: {gguf_output_name}")
