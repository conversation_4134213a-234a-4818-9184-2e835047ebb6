# data_loader.py
from datasets import load_dataset

def get_dataset(jsonl_path):
    dataset = load_dataset("json", data_files=jsonl_path, split="train")
    
    def format_prompt(example):
        return {
            "text": f"<|user|>\n{example['prompt']}\n<|assistant|>\n{example['response']}"
        }

    return dataset.map(format_prompt)

def get_dataset_new(example):
    history = "\n".join(example["history"])
    current = example["current"]
    prompt = f"<|user|>\n{history}\n{current}\n<|assistant|>"
    response = example["target"]
    return {
        "text": f"{prompt}\n{response}"
    }