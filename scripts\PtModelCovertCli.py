# 兼容 __file__ 在调试器/Run Code 中可能为未定义的问题
# only for debugging from vscode ide
import sys
from pathlib import Path

def get_script_path():
    try:
        # 脚本正常运行、终端运行
        return Path(__file__).resolve()
    except NameError:
        # VS Code Run Code 或某些嵌入环境
        if len(sys.argv) > 0:
            return Path(sys.argv[0]).resolve()
        else:
            # 交互式环境兜底方案（非推荐）
            return Path.cwd() / "UNKNOWN_SCRIPT.py"
    except Exception:
        return Path.cwd()
    
# 获取当前脚本路径（不硬编码）
script_path = get_script_path()
parent_dir = script_path.parent.parent
sys.path.append(str(parent_dir))

try:
    base_path = Path(__file__).resolve()
except NameError:
    # VS Code "Run Code" 或调试时 __file__ 可能不存在，手动指定一个默认路径
    base_path = Path(sys.argv[0]).resolve() if sys.argv[0] else Path.cwd() / "saveModel2HF.py"
    parent_dir = base_path.parent.parent
    sys.path.append(str(parent_dir))

import argparse
import torch
from safetensors.torch import save_file, load_file
from transformers import AutoTokenizer, AutoModelForCausalLM


def convert_pt_to_safetensors(pt_path, safe_path):
    state_dict = torch.load(pt_path, map_location="cpu")
    if not isinstance(state_dict, dict):
        raise ValueError("模型文件不是 state_dict 格式，请确认保存方式")
    save_file(state_dict, safe_path)
    print(f"✔ 转换完成: {safe_path}")


def load_model_from_safetensors(safe_path, model_name):
    model = AutoModelForCausalLM.from_pretrained(model_name, low_cpu_mem_usage=True)
    state_dict = load_file(safe_path)
    model.load_state_dict(state_dict, strict=False)
    model.eval()
    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
    return model, tokenizer


def chat(model, tokenizer, prompt):
    inputs = tokenizer(prompt, return_tensors="pt")
    outputs = model.generate(**inputs, max_new_tokens=50)
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response


def main():
    parser = argparse.ArgumentParser(description="Convert .pt to .safetensors and chat")
    parser.add_argument("--pt", type=str, help="Path to .pt model file")
    parser.add_argument("--safe", type=str, help="Path to save .safetensors file")
    parser.add_argument("--model", type=str, help="HuggingFace model ID (e.g. Qwen/Qwen1.5-0.5B)")
    parser.add_argument("--prompt", type=str, help="Prompt to send to model")
    
    defSafePath = "D:/Models-Safetensor/MiniMind2"
    defModel = "model.safetensors"

    args = parser.parse_args()
    
    if(args.pt == None or args.safe == None or args.model == None or args.prompt == None):
        print("Please provide all arguments")
        exit()
    if(args.pt == None or args.safe == None or args.model == None or args.prompt != None):
        print("Please provide all arguments")
        exit()
    elif(args.pt == None and args.safe != None and args.model != None and args.prompt != None):
        load_model_from_safetensors(args.safe, args.model)
        response = chat(model, tokenizer, args.prompt)
        print("\n🧠 模型回复: ", response)
        
    elif(args.pt != None and args.safe != None and args.model != None and args.prompt != None):
        convert_pt_to_safetensors(args.pt, args.safe)
        model, tokenizer = load_model_from_safetensors(args.safe, args.model)
        if args.prompt:
            response = chat(model, tokenizer, args.prompt)
            print("\n🧠 模型回复: ", response)


if __name__ == "__main__":
    main()