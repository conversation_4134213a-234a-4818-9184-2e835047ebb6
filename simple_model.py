# 1. 导入依赖
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import datasets, transforms

# 2. 定义网络结构
class SimpleNet(nn.Module):
    def __init__(self):
        super().__init__()
        self.fc1 = nn.Linear(28*28, 128)
        self.fc2 = nn.Linear(128, 10)

    def forward(self, x):
        x = x.view(-1, 28*28)      # 展平
        x = F.relu(self.fc1(x))
        x = self.fc2(x)            # 注意：不做 softmax，输出 logits
        return x

model = SimpleNet()

# 3. 准备数据
train_loader = torch.utils.data.DataLoader(
    datasets.MNIST('./data', train=True, download=True,
                   transform=transforms.ToTensor()),
    batch_size=64, shuffle=True
)

# 4. 训练模型（1 个 epoch 简化演示）
optimizer = torch.optim.Adam(model.parameters())
loss_fn = nn.CrossEntropyLoss()

model.train()
for batch_x, batch_y in train_loader:
    logits = model(batch_x)
    loss = loss_fn(logits, batch_y)

    optimizer.zero_grad()
    loss.backward()
    optimizer.step()

print("✅ 模型训练完成")

# 生成一个 dummy 输入
dummy_input = torch.randn(1, 1, 28, 28)

# 导出模型
torch.onnx.export(
    model, dummy_input,
    "mnist_model.onnx",
    input_names=["input"],
    output_names=["output"],
    dynamic_axes={"input": {0: "batch_size"}, "output": {0: "batch_size"}},
    opset_version=11
)

print("✅ 模型已导出为 mnist_model.onnx")




import onnxruntime as ort
import numpy as np

# 加载模型
ort_session = ort.InferenceSession("mnist_model.onnx")

# 准备输入（numpy 格式）
sample_input = torch.randn(1, 1, 28, 28).numpy()

# 推理
outputs = ort_session.run(
    None,
    {"input": sample_input}
)

logits = outputs[0]
pred = np.argmax(logits, axis=1)
print("✅ 推理结果（预测类别）:", pred)

# 查看模型结构
ort.get_model_meta("mnist_model.onnx")
ort.get_model_graph_string("mnist_model.onnx")

# 生成python快速排序算法
