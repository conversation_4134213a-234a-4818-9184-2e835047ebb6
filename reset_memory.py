import jieba
import json
from logging import Logger
from flask import Flask, request, Response


#jieba.load_userdict("user_dict.txt")  # 加载自定义词典

from collections import deque
# 实体跟踪器
# 用于跟踪和管理多轮对话中的实体，包括历史记录和当前焦点实体
class EntityTracker:
    def __init__(self, max_memory=3):
        self.history = deque(maxlen=max_memory)
        self.current_focus = None

    def update(self, entities):
        # 实体权重计算
        weights = {}
        for ent in entities:
            weights[ent] = weights.get(ent, 0) + 1
            if ent in self.history:
                weights[ent] *= 1.5

        # 焦点实体选择
        self.current_focus = max(weights, key=weights.get, default=None)
        self.history.append(self.current_focus)


class MultiRoundQuestionMemory:
# 用于多轮问题记忆和重构
# 该类用于跟踪多轮对话中的问题上下文，并在需要时重构问题以保持上下文一致性

    # 初始化方法
    # memory_rounds: 记忆轮数，默认3轮
    def __init__(self, memory_rounds=3):
        """初始化记忆轮数和上下文存储"""
        self.memory_rounds = memory_rounds  # 记忆轮数，默认3轮
        self.context = []  # 存储问题下文， todo:可能未来要存到数据库
        self.original_subject = None  # 原始问题主题

    # 重置记忆上下文
    def reset_memory(self):
        """重置记忆上下文"""
        self.context = []
        self.original_subject = None

    # 设置记忆轮数
    # rounds: 新的记忆轮数 
    def set_memory_rounds(self, rounds):
        """设置记忆轮数"""
        self.memory_rounds = rounds

    # 对问题进行分词处理，并提取主题词  
    def track_question(self, question):
        """对问题分词处理"""
        if not question or not isinstance(question, str):
            raise ValueError("问题不能为空且必须是字符串")
        return jieba.lcut(question)  # 使用结巴分词

    def get_subject(self, tokens):
        """提取问题中的主题词
        Args:
            tokens: 分词后的词语列表
        Returns:
            合并后的主题字符串
        """
        # 过滤掉一些常见的疑问词、语气词和动词
        non_subject_words = ['多少', '呢', '?', '吗','嘛', '的', '是', '有', '人', '它', '做', '怎么', '如何']
        subject_tokens = []
        for token in tokens:
            if token not in non_subject_words:
                subject_tokens.append(token)
        return "".join(subject_tokens)

    def reconstruct_question(self, current_question):
        raise ValueError("问题不能为空且必须是字符串")
        if not self.context:
            current_tokens = self.track_question(current_question)
            self.original_subject = self.get_subject(current_tokens)
            self.context.append(current_question)
            return current_question
        current_tokens = self.track_question(current_question)
        current_subject = self.get_subject(current_tokens)
        last_reconstructed_question = self.context[-1]

        # 检查当前问题是否包含代词，
        # 如果包含代词，则尝试替换为上一个问题的主体
        # todo: 对于钢种处理，需要重写这里的代码，比如合金钢，碳钢什么的
        if self.original_subject and ('它' in current_question or '它们' in current_question):
            # 先尝试精确替换
            new_question = current_question.replace('它', f'({self.original_subject})').replace('它们', f'({self.original_subject})')
            new_question = new_question.replace('它?', f'{self.original_subject}?').replace('它们?', f'{self.original_subject}?')

            # 如果没有变化，则使用原始替换逻辑
            if new_question == current_question:
                new_question = current_question.replace('它', self.original_subject).replace('它们', self.original_subject)
            # 检查当前问题是否缺少主体，且上一个问题有主体
            elif self.original_subject and (not current_subject or len(current_subject) < len(self.original_subject)):
                # 处理特殊情况，如“男生”“女生”这类
                if current_subject in ['男生', '女生'] and self.original_subject:
                    # 找到上一轮重构问题中主体之后的内容
                    last_question_body = last_reconstructed_question[len(self.get_subject(self.track_question(last_reconstructed_question))):]
                    new_question = f"{self.original_subject}{last_question_body}"
                else:
                    # 只有当当前问题确实缺少主体时才拼接
                    if not current_subject:
                        new_question = f"{self.original_subject}{current_question}"
                    else:
                        new_question = current_question
        else:
            new_question = current_question

        self.context.append(new_question)
        if len(self.context) > self.memory_rounds:
            self.context.pop(0)
        return new_question

from flask import Flask, request, jsonify
app = Flask(__name__)
memory = MultiRoundQuestionMemory(memory_rounds=3)

# 问题重构API端点
# 简化版
# 重构问题的API端点，接收JSON格式的请求体，包含一个问题字段
#
# 返回重构后的问题和原始问题
# 如果请求体无效或缺少问题字段，则返回400错误   
# 调用示例
# curl -X POST http://localhost:5001/reconstructSimple -H "Content-Type: application/json" -d '{"question": "它的钢种是什么？"}'
# curl -X POST -H "Content-Type: application/json" -d '{"question":"男生多少人？"}' http://localhost:5000/reconstruct

@app.route('/reconstructSimple', methods=['POST'])
def reconstructSimple():
    data = request.get_json()
    if not data or 'question' not in data:
        return jsonify({'error': 'Invalid request, question is required'}), 400
    question = data['question']
    try:
        reconstructed = memory.reconstruct_question(question)
        return jsonify({
            'original_question': question,
           'reconstructed_question': reconstructed
        })
    except ValueError as e:
        return jsonify({'error': str(e)}), 400

@app.route('/reconstruct', methods=['POST'])
def reconstruct():
    """问题重构API端点"""
    logger.info("Received reconstruct request")  # 获取JSON请求体
    data = request.get_json()

    # 验证请求数据
    if not data or 'question' not in data:
        logger.warning("Invalid request received - missing question")
        error_data = json.dumps({'error': 'Invalid request, question is required'}, ensure_ascii=False)
        return Response(error_data, status=400, mimetype='application/json')

    # 处理Unicode转义字符
    question = data['question'].encode('unicode-escape').decode('unicode-escape')
    logger.debug("Original question: %s", question)

    try:
        # 重构问题并返回响应
        reconstructed = memory.reconstruct_question(question)
        logger.info("Question reconstructed: %s -> %s", question, reconstructed)

        response_data = json.dumps({
            "original_question": question,
            "reconstructed_question": reconstructed
        }, ensure_ascii=False)  # 确保中文不转义
        return Response(response_data, status=200, mimetype='application/json; charset=utf-8')
    except ValueError as e:
        logger.error("Error during question reconstruction: %s", str(e))
        error_data = json.dumps({'error': str(e)}, ensure_ascii=False)
        return Response(error_data, status=400, mimetype='application/json')

@app.route('/resetSimple', methods=['POST'])
def resetSimple():
    memory.reset_memory()
    return jsonify({'status': 'memory reset successfully'})

@app.route('/reset', methods=['POST'])
def reset():
    """重置记录API端点"""
    logger.info("Received memory_reset request")
    memory.reset_memory()
    logger.info("Memory reset completed")
    response_data = json.dumps({'status':'memory reset successfully'}, ensure_ascii=False)
    return Response(response_data, mimetype='application/json')

if __name__ == "__main__":
    # 启动Flask应用
    logger.info("Starting Memory RAG service on port 5001")
    app.run(host='0.0.0.0', port=5001, debug=True)
