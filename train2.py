import os
import torch
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    BitsAndBytesConfig,
    Trainer,
    TrainingArguments,
    DataCollatorForLanguageModeling,
)
from datasets import load_dataset
from peft import prepare_model_for_kbit_training, get_peft_model, LoraConfig, TaskType
from torch.backends.cuda import sdp_kernel, SDPBackend
from contextlib import nullcontext

# ========== 确认 Flash Attention 启用 ==========
print("CUDA available:", torch.cuda.is_available())
print("Torch version:", torch.__version__)
if hasattr(torch.backends.cuda, "sdp_kernel"):
    try:
        torch.backends.cuda.sdp_kernel.enable_flash(True)
        torch.backends.cuda.sdp_kernel.set_backend(SDPBackend.FLASH_ATTENTION)
        print("✅ Flash Attention 启用成功")
    except Exception as e:
        print("⚠️ 启用 Flash Attention 失败:", e)
else:
    print("⚠️ 当前 PyTorch 版本不支持 Flash Attention 设置")

# ========== 基本参数 ==========
model_id = "Qwen/Qwen3-0.6B"
data_path = "trainDatasets.jsonl"
output_dir = "./lora-qwen3-0.6b"

# ========== 加载分词器 ==========
tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
tokenizer.pad_token = tokenizer.eos_token

# ========== 加载数据集并tokenize ==========
def preprocess(example):
    prompt = example["prompt"]
    response = example["response"]
    tokenized = tokenizer(prompt, truncation=True, padding="max_length", max_length=512)
    target_tokenized = tokenizer(response, truncation=True, padding="max_length", max_length=512)
    tokenized["labels"] = target_tokenized["input_ids"]
    return tokenized

dataset = load_dataset("json", data_files=data_path, split="train")
dataset = dataset.map(preprocess, remove_columns=dataset.column_names)

# ========== 加载模型 & LoRA 配置 ==========
# model = AutoModelForCausalLM.from_pretrained(
#     model_id,
#     load_in_4bit=True,
#     device_map="auto",
#     trust_remote_code=True,
# )
# model = prepare_model_for_kbit_training(model)
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4",
)

model = AutoModelForCausalLM.from_pretrained(
    model_id,
    quantization_config=bnb_config,
    device_map="auto",
    trust_remote_code=True,
)

peft_config = LoraConfig(
    r=8,
    lora_alpha=16,
    target_modules=["q_proj", "v_proj"],
    lora_dropout=0.05,
    bias="none",
    task_type=TaskType.CAUSAL_LM,
)
model = get_peft_model(model, peft_config)

# ========== 编译模型（如 PyTorch >= 2.0） ==========
# if hasattr(torch, "compile"):
#     model = torch.compile(model)
#     print("✅ 启用 torch.compile 编译加速")

# ========== 训练配置 ==========
training_args = TrainingArguments(
    per_device_train_batch_size=8,
    gradient_accumulation_steps=4,
    num_train_epochs=5,
    learning_rate=2e-4,
    fp16=True,  # 自动混合精度
    logging_steps=5,
    output_dir=output_dir,
    save_strategy="epoch",
    save_total_limit=1,
    remove_unused_columns=False,
    report_to="none",
)

# ========== 启动训练 ==========
trainer = Trainer(
    model=model,
    tokenizer=tokenizer,
    args=training_args,
    train_dataset=dataset,
    data_collator=DataCollatorForLanguageModeling(tokenizer, mlm=False),
)

trainer.train()