from transformers import PreTrainedModel
import torch.nn as nn

class MiniMindForCausalLM(PreTrainedModel):
    config_class = MiniMindConfig  # 指定配置类
    base_model_prefix = "minimind"  # 用于 HuggingFace 权重 key 命名
    supports_gradient_checkpointing = True

    def __init__(self, config):
        super().__init__(config)
        self.emb = nn.Embedding(32000, config.hidden_size)
        self.blocks = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=config.hidden_size,
                nhead=8
            ) for _ in range(config.num_hidden_layers)
        ])
        self.norm = nn.LayerNorm(config.hidden_size)
        self.lm_head = nn.Linear(config.hidden_size, 32000, bias=False)

        self.post_init()  # <- 必须，注册 config、初始化权重等

    def forward(self, input_ids, attention_mask=None, labels=None):
        x = self.emb(input_ids)
        for block in self.blocks:
            x = block(x)
        x = self.norm(x)
        logits = self.lm_head(x)

        loss = None
        if labels is not None:
            shift_logits = logits[:, :-1, :].contiguous()
            shift_labels = labels[:, 1:].contiguous()
            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(shift_logits.view(-1, shift_logits.size(-1)),
                            shift_labels.view(-1))

        return {"loss": loss, "logits": logits}
