import zipfile
import os

# 重新创建 MoE 工业预测模型代码结构
base_dir = "/mnt/data/moe_industrial_model"
os.makedirs(base_dir, exist_ok=True)

# 文件内容定义
moe_script = """
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.model_selection import train_test_split
import pandas as pd
import matplotlib.pyplot as plt
import random

# 配置
NUM_EXPERTS = 4
INPUT_DIM = 6
HIDDEN_DIM = 32
OUTPUT_DIM = 1
EPOCHS = 100
BATCH_SIZE = 32
LR = 0.001

class Expert(nn.Module):
    def __init__(self):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(INPUT_DIM, HIDDEN_DIM),
            nn.ReLU(),
            nn.Linear(HIDDEN_DIM, OUTPUT_DIM)
        )

    def forward(self, x):
        return self.net(x)

class Router(nn.Module):
    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(INPUT_DIM, NUM_EXPERTS)

    def forward(self, x):
        return F.softmax(self.linear(x), dim=1)

class MoE(nn.Module):
    def __init__(self):
        super().__init__()
        self.experts = nn.ModuleList([Expert() for _ in range(NUM_EXPERTS)])
        self.router = Router()

    def forward(self, x):
        weights = self.router(x)
        expert_outputs = torch.stack([expert(x) for expert in self.experts], dim=2)
        output = torch.sum(expert_outputs * weights.unsqueeze(2), dim=1)
        return output

def load_data():
    df = pd.read_csv("simulated_casting_temp_data.csv")
    X = df.drop(columns=["target"]).values
    y = df["target"].values.reshape(-1, 1)
    return train_test_split(torch.tensor(X, dtype=torch.float32),
                            torch.tensor(y, dtype=torch.float32),
                            test_size=0.2, random_state=42)

def train(model, X_train, y_train, X_test, y_test):
    optimizer = torch.optim.Adam(model.parameters(), lr=LR)
    criterion = nn.MSELoss()
    train_losses, test_losses = [], []

    for epoch in range(EPOCHS):
        model.train()
        indices = list(range(len(X_train)))
        random.shuffle(indices)
        for i in range(0, len(indices), BATCH_SIZE):
            idx = indices[i:i + BATCH_SIZE]
            x_batch = X_train[idx]
            y_batch = y_train[idx]
            pred = model(x_batch)
            loss = criterion(pred, y_batch)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

        model.eval()
        with torch.no_grad():
            train_loss = criterion(model(X_train), y_train).item()
            test_loss = criterion(model(X_test), y_test).item()
        train_losses.append(train_loss)
        test_losses.append(test_loss)
        print(f"Epoch {epoch+1}/{EPOCHS}, Train Loss: {train_loss:.4f}, Test Loss: {test_loss:.4f}")

    # 画图
    plt.plot(train_losses, label='Train Loss')
    plt.plot(test_losses, label='Test Loss')
    plt.legend()
    plt.title("MoE Training Loss")
    plt.savefig("moe_loss_plot.png")

if __name__ == "__main__":
    X_train, X_test, y_train, y_test = load_data()
    model = MoE()
    train(model, X_train, y_train, X_test, y_test)
"""

sample_data = """feature1,feature2,feature3,feature4,feature5,feature6,target
0.5,1.2,35,2.5,0.8,3.0,1538
0.7,1.3,32,2.7,0.7,2.8,1541
0.4,1.1,36,2.4,0.9,3.1,1536
0.6,1.4,33,2.6,0.6,2.9,1543
0.5,1.2,34,2.5,0.7,3.0,1539
"""

# 写入脚本和数据文件
with open(os.path.join(base_dir, "moe_industrial_predictor.py"), "w") as f:
    f.write(moe_script)

with open(os.path.join(base_dir, "simulated_casting_temp_data.csv"), "w") as f:
    f.write(sample_data)

# 打包为zip
zip_path = "/mnt/data/moe_industrial_model.zip"
with zipfile.ZipFile(zip_path, 'w') as zipf:
    for root, _, files in os.walk(base_dir):
        for file in files:
            full_path = os.path.join(root, file)
            arcname = os.path.relpath(full_path, base_dir)
            zipf.write(full_path, arcname)

zip_path
