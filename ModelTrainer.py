from fastapi import FastAPI
import subprocess
from typing import List, Dict
from pydantic import BaseModel
from collections import deque

app = FastAPI()

# 存储对话历史的字典,key是会话id,value是一个双端队列
conversation_history: Dict[str, deque] = {}

class ChatMessage(BaseModel):
    session_id: str
    message: str

class ChatResponse(BaseModel):
    response: str

def generate_response(prompt: str) -> str:
    """调用模型生成回复"""
    model_name = "qwen3-0.6B-hansen"
    command = f"mlx_lm.generate --model {model_name} --prompt '{prompt}'"

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return f"错误: {e.stderr}"

def get_history_prompt(history: List[str], new_message: str) -> str:
    """将历史记录和新消息拼接成完整的prompt"""
    prompt = ""
    for i, msg in enumerate(history):
        if i % 2 == 0:
            prompt += f"{msg},"
    prompt += f"{new_message}"
    print(prompt)
    return prompt

@app.post("/chat", response_model=ChatResponse)
async def chat(chat_msg: ChatMessage):
    session_id = chat_msg.session_id

    # 如果是新会话,初始化历史记录
    if session_id not in conversation_history:
        conversation_history[session_id] = deque(maxlen=10)  # 存储5轮对话(问答算一次)

    # 获取当前会话的历史记录
    history = list(conversation_history[session_id])

    # 生成完整的prompt
    prompt = get_history_prompt(history, chat_msg.message)

    # 调用模型生成回复
    response = await generate_response(prompt)
    print(f"{session_id}: {response}")

    # 更新历史记录
    conversation_history[session_id].append(chat_msg.message)
    conversation_history[session_id].append(response)

    return ChatResponse(response=response)

# 清除会话
@app.delete("/chat/{session_id}")
async def clear_history(session_id: str):
    """清除指定会话的历史记录"""
    if session_id in conversation_history:
        del conversation_history[session_id]
    return {"message": "历史记录已清除"}

# 获取会话历史记录
@app.get("/chat/history/{session_id}")
async def get_history(session_id: str):
    """获取指定会话的历史记录"""
    if session_id in conversation_history:
        return {"history": list(conversation_history[session_id])}
    else:
        return {"message": "会话不存在或历史记录为空"}
    
# 获取所有会话ID
@app.get("/chat/sessions")
async def get_sessions():
    """获取所有会话ID"""
    return {"sessions": list(conversation_history.keys())}

# 根路由
@app.get("/")
async def root():
    return {"message": "欢迎使用对话模型API! 使用 /chat 进行对话, /chat/history/{session_id} 获取历史记录, /chat/sessions 获取所有会话ID, /chat/{session_id} 清除指定会话的历史记录."}

# 健康检查接口
@app.get("/chat/health")
async def health_check():
    """健康检查接口"""
    return {"status": "ok", "message": "对话模型API运行正常!"}  

@app.get("/chat/clear_all")
async def clear_all():
    """清除所有会话的历史记录"""
    conversation_history.clear()
    return {"message": "所有会话的历史记录已清除"}

@app.get("/chat/history")
async def get_all_history():
    """获取所有会话的历史记录"""
    all_history = {session_id: list(history) for session_id, history in conversation_history.items()}
    return {"all_history": all_history}

@app.get("/chat/history_count")
async def get_history_count():
    """获取所有会话的历史记录条数"""
    history_count = {session_id: len(history) for session_id, history in conversation_history.items()}
    return {"history_count": history_count}
@app.get("/chat/history_length/{session_id}")
async def get_history_length(session_id: str):
    """获取指定会话的历史记录长度"""
    if session_id in conversation_history:
        return {"length": len(conversation_history[session_id])}
    else:
        return {"message": "会话不存在或历史记录为空"}
@app.get("/chat/history_last/{session_id}")
async def get_last_message(session_id: str):
    """获取指定会话的最后一条消息"""
    if session_id in conversation_history and conversation_history[session_id]:
        return {"last_message": conversation_history[session_id][-1]}
    else:
        return {"message": "会话不存在或历史记录为空"}
@app.get("/chat/history_first/{session_id}")
async def get_first_message(session_id: str):
    """获取指定会话的第一条消息"""
    if session_id in conversation_history and conversation_history[session_id]:
        return {"first_message": conversation_history[session_id][0]}
    else:
        return {"message": "会话不存在或历史记录为空"}
    
@app.get("/chat/history_all/{session_id}")
async def get_all_messages(session_id: str):
    """获取指定会话的所有消息"""
    if session_id in conversation_history:
        return {"all_messages": list(conversation_history[session_id])}
    else:
        return {"message": "会话不存在或历史记录为空"}
@app.get("/chat/history_contains/{session_id}/{message}")
async def history_contains(session_id: str, message: str):
    """判断指定会话是否包含某个特定的消息"""
    if session_id in conversation_history:
        if message in conversation_history[session_id]:
            return {"result": True}
        else:
            return {"result": False}
    else:
        return {"message": "会话不存在或历史记录为空"}
@app.get("/chat/history_count_message/{session_id}/{message}")
async def count_message(session_id: str, message: str):
    """统计指定会话中特定消息出现的次数"""
    if session_id in conversation_history:
        count = conversation_history[session_id].count(message)
        return {"count": count}
    else:
        return {"message": "会话不存在或历史记录为空"}
@app.get("/chat/history_last_n/{session_id}/{n}")
async def get_last_n_messages(session_id: str, n: int):
    """获取指定会话的最后N条消息"""
    if session_id in conversation_history:
        last_n_messages = list(conversation_history[session_id])[-n:]
        return {"last_n_messages": last_n_messages}
    else:
        return {"message": "会话不存在或历史记录为空"}
    
@app.get("/chat/history_first_n/{session_id}/{n}")
async def get_first_n_messages(session_id: str, n: int):
    """获取指定会话的前N条消息"""
    if session_id in conversation_history:
        first_n_messages = list(conversation_history[session_id])[:n]
        return {"first_n_messages": first_n_messages}
    else:
        return {"message": "会话不存在或历史记录为空"}
@app.get("/chat/history_random/{session_id}")
async def get_random_message(session_id: str):  
    """获取指定会话的随机一条消息"""
    if session_id in conversation_history and conversation_history[session_id]:
        import random
        random_message = random.choice(list(conversation_history[session_id]))
        return {"random_message": random_message}
    else:
        return {"message": "会话不存在或历史记录为空"}
@app.get("/chat/history_contains_word/{session_id}/{word}")
async def history_contains_word(session_id: str, word: str):
    """判断指定会话的历史记录中是否包含某个单词"""
    if session_id in conversation_history:
        contains = any(word in msg for msg in conversation_history[session_id])
        return {"contains": contains}
    else:
        return {"message": "会话不存在或历史记录为空"}
@app.get("/chat/history_word_count/{session_id}/{word}")
async def count_word_in_history(session_id: str, word: str):
    """统计指定会话的历史记录中某个单词出现的次数"""
    if session_id in conversation_history:
        count = sum(msg.count(word) for msg in conversation_history[session_id])
        return {"count": count}
    else:
        return {"message": "会话不存在或历史记录为空"}
    
@app.get("/chat/history_length_all")
async def get_all_history_length():
    """获取所有会话的历史记录长度"""
    all_lengths = {session_id: len(history) for session_id, history in conversation_history.items()}
    return {"all_lengths": all_lengths}
@app.get("/chat/history_last_n_all/{n}")
async def get_last_n_messages_all(n: int):
    """获取所有会话的最后N条消息"""
    all_last_n_messages = {session_id: list(history)[-n:] for session_id, history in conversation_history.items() if len(history) >= n}
    return {"all_last_n_messages": all_last_n_messages}
@app.get("/chat/history_first_n_all/{n}")
async def get_first_n_messages_all(n: int):
    """获取所有会话的前N条消息"""
    all_first_n_messages = {session_id: list(history)[:n] for session_id, history in conversation_history.items() if len(history) >= n}
    return {"all_first_n_messages": all_first_n_messages}
@app.get("/chat/history_random_all")
async def get_random_message_all():
    """获取所有会话的随机一条消息"""
    import random
    all_random_messages = {}
    for session_id, history in conversation_history.items():
        if history:
            random_message = random.choice(list(history))
            all_random_messages[session_id] = random_message
    return {"all_random_messages": all_random_messages}

@app.get("/chat/history_contains_word_all/{word}")
async def history_contains_word_all(word: str):
    """判断所有会话的历史记录中是否包含某个单词"""
    contains_all = {}
    for session_id, history in conversation_history.items():
        contains = any(word in msg for msg in history)
        contains_all[session_id] = contains
    return {"contains_all": contains_all}
@app.get("/chat/history_word_count_all/{word}")
async def count_word_in_history_all(word: str):
    """统计所有会话的历史记录中某个单词出现的次数"""
    word_count_all = {}
    for session_id, history in conversation_history.items():
        count = sum(msg.count(word) for msg in history)
        word_count_all[session_id] = count
    return {"word_count_all": word_count_all}
@app.get("/chat/history_contains_all/{message}")
async def history_contains_all(message: str):
    """判断所有会话的历史记录中是否包含某个特定的消息"""
    result = {}
    for session_id, history in conversation_history.items():
        if message in history:
            result[session_id] = True
        else:
            result[session_id] = False
    return {"result": result}
@app.get("/chat/history_count_all/{message}")
async def count_message_all(message: str):
    """统计所有会话中特定消息出现的次数"""
    count_all = {}
    for session_id, history in conversation_history.items():
        count = history.count(message)
        count_all[session_id] = count
    return {"count_all": count_all}
@app.get("/chat/history_length_all_sessions")
async def get_all_history_length_sessions():
    """获取所有会话的历史记录长度"""
    all_lengths = {session_id: len(history) for session_id, history in conversation_history.items()}
    return {"all_lengths": all_lengths}

if __name__ == "__main__":
    import uvicorn
    #uvicorn.run("main:app", host="localhost", port=8000, reload=True)
    uvicorn.run(app, host="0.0.0.0", port=8022)


#模型保存命令
# mlx_lm.fuse --model /Users/<USER>/qwen2.5-0.5B-hanshan --adapter-path adapters --save-path qwen2.5-0.5B-fused
# mlx_lm.save --model qwen3-0.6B-hansn --output /path/to/save/model
# 模型加载命令
# mlx_lm.load --model /path/to/save/model --name qwen2.5-0.5B-hanshan
# 模型生成命令
# mlx_lm.generate --model qwen2.5-0.5B-hanshan --prompt "你的提示词"
# mlx_lm.generate --model qwen2-5-0.5B-hanshan --prompt '北京今天的天气怎么样？明天呢'
# 模型评估命令 
# mlx_lm.evaluate --model qwen2.5-0.5B-hanshan --dataset /path/to/dataset
# 模型训练命令
# mlx_lm.train --model qwen2.5-0.5B-hanshan --dataset /path/to/dataset --epochs 3
# 模型推理命令
# mlx_lm.infer --model qwen2.5-0.5B-hanshan --input "你的输入"
# 模型转换命令
# mlx_lm.convert --model qwen2.5-0.5B-hanshan --format onnx
# 模型优化命令
# mlx_lm.optimize --model qwen2.5-0.5B-hanshan --technique quantization
# 模型部署命令
# mlx_lm.deploy --model qwen2.5-0.5B-hanshan --platform docker
# 模型监控命令
# mlx_lm.monitor --model qwen2.5-0.5B-hanshan --metrics accuracy,latency
# 模型版本管理命令
# mlx_lm.version --model qwen2.5-0.5B-hanshan --version 1.0.0
# 模型日志命令
# mlx_lm.log --model qwen2.5-0.5B-hanshan --level info
# 模型配置命令
# mlx_lm.config --model qwen2.5-0.5B-hanshan --setting batch_size=32,learning_rate=0.001
# 模型数据预处理命令
# mlx_lm.preprocess --dataset /path/to/dataset --output /path/to/preprocessed_data
# 模型后处理命令
# mlx_lm.postprocess --output /path/to/output --format json
# 模型集成命令
# mlx_lm.integrate --models qwen2.5-0.5B-hanshan,another-model --method ensemble
# 模型可视化命令
# mlx_lm.visualize --model qwen2.5-0.5B-hanshan --type attention
# 模型调试命令
# mlx_lm.debug --model qwen2.5-0.5B-hanshan --input "你的输入" --output /path/to/debug_output
# 模型导出命令
# mlx_lm.export --model qwen2.5-0.5B-hanshan --format pytorch
# 模型导入命令
# mlx_lm.import --model /path/to/imported_model --name qwen2.5-0.5B-hanshan
# 模型测试命令
# mlx_lm.test --model qwen2.5-0.5B-hanshan --dataset /path/to/test_dataset
# 模型评估指标命令
# mlx_lm.metric --model qwen2.5-0.5B-hanshan --dataset /path/to/test_dataset --metric accuracy
# 模型批量处理命令
# mlx_lm.batch --model qwen2.5-0.5B-hanshan --inputs /path/to/inputs --output /path/to/output
# 模型配置文件命令
# mlx_lm.config --model qwen2.5-0.5B-hanshan --setting batch_size=32,learning_rate=0.001
# 模型参数调整命令
# mlx_lm.tune --model qwen2.5-0.5B-hanshan --params learning_rate=0.0001,dropout=0.1
# 模型数据增强命令
# mlx_lm.augment --dataset /path/to/dataset --output /path/to/augmented_data
# 模型迁移学习命令
# mlx_lm.transfer --model qwen2.5-0.5B-hanshan --source_model /path/to/source_model --dataset /path/to/dataset
# 模型分布式训练命令
# mlx_lm.distributed --model qwen2.5-0.5B-hanshan --strategy ddp --nproc_per_node 2
# 模型自动化训练命令
# mlx_lm.auto_train --model qwen2.5-0.5B-hanshan --dataset /path/to/dataset --epochs 5 --batch_size 16
# 模型超参数优化命令
# mlx_lm.hyperopt --model qwen2.5-0.5B-hanshan --dataset /path/to/dataset --search_space learning_rate=[0.0001, 0.001, 0.01],batch_size=[16, 32]
# 模型知识蒸馏命令
# mlx_lm.distill --model qwen2.5-0.5B-hanshan --teacher_model /path/to/teacher_model --dataset /path/to/dataset
# 模型量化命令
# mlx_lm.quantize --model qwen2.5-0.5B-hanshan --technique dynamic
# 模型剪枝命令
# mlx_lm.prune --model qwen2.5-0.5B-hanshan --technique l1
# 模型压缩命令
# mlx_lm.compress --model qwen2.5-0.5B-hanshan --technique bert
# 模型集成命令
# mlx_lm.ensemble --models qwen2.5-0.5B-hanshan,another-model --method stacking
# 模型在线学习命令
# mlx_lm.online --model qwen2.5-0.5B-hanshan --new_data /path/to/new_data
# 模型批量推理命令
# mlx_lm.batch_infer --model qwen2.5-0.5B-hanshan --inputs /path/to/inputs --output /path/to/output
# 模型自适应命令
# mlx_lm.adaptive --model qwen2.5-0.5B-hanshan --task classification --dataset /path/to/dataset
# 模型预测命令
# mlx_lm.predict --model qwen2.5-0.5B-hanshan --input "你的输入"
# 模型评估命令
# mlx_lm.evaluate --model qwen2.5-0.5B-hanshan --dataset /path/to/dataset --metrics accuracy,f1_score
# 模型可解释性命令
# mlx_lm.explain --model qwen2.5-0.5B-hanshan --input "你的输入" --mode lime
# 模型可视化命令
# mlx_lm.visualize --model qwen2.5-0.5B-hanshan --type attention
# 模型日志命令
# mlx_lm.log --model qwen2.5-0.5B-hanshan --level info
# 模型监控命令
# mlx_lm.monitor --model qwen2.5-0.5B-hanshan --metrics accuracy,latency
# 模型版本管理命令
# mlx_lm.version --model qwen2.5-0.5B-hanshan --version 1.0.0
# 模型调试命令
# mlx_lm.debug --model qwen2.5-0.5B-hanshan --input "你的输入" --output /path/to/debug_output
# 模型导出命令
# mlx_lm.export --model qwen2.5-0.5B-hanshan --format pytorch
# 模型导入命令
# mlx_lm.import --model /path/to/imported_model --name qwen2.5-0.5B-hanshan
# 模型测试命令
# mlx_lm.test --model qwen2.5-0.5B-hanshan --dataset /path/to