{"architectures": ["MiniMindForCausalLM"], "auto_map": {"AutoConfig": "model_minimind.MiniMindConfig", "AutoModelForCausalLM": "model_minimind.MiniMindForCausalLM"}, "aux_loss_alpha": 0.1, "bos_token_id": 1, "dropout": 0.0, "eos_token_id": 2, "flash_attn": true, "hidden_act": "silu", "hidden_size": 512, "intermediate_size": 1408, "max_position_embeddings": 32768, "max_seq_len": 512, "model_type": "minimind", "n_routed_experts": 4, "n_shared_experts": 1, "norm_topk_prob": true, "num_attention_heads": 8, "num_experts_per_tok": 2, "num_hidden_layers": 8, "num_key_value_heads": 2, "rms_norm_eps": 1e-05, "rope_theta": 1000000.0, "scoring_func": "softmax", "seq_aux": true, "torch_dtype": "bfloat16", "transformers_version": "4.48.0", "use_moe": false, "vocab_size": 6400}