{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 15.0, "eval_steps": 500, "global_step": 90, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 1.6666666666666665, "grad_norm": 2.11081600189209, "learning_rate": 0.00018500000000000002, "loss": 3.2554, "step": 10}, {"epoch": 3.3333333333333335, "grad_norm": 2.120530843734741, "learning_rate": 0.00016833333333333335, "loss": 2.4936, "step": 20}, {"epoch": 5.0, "grad_norm": 2.4215469360351562, "learning_rate": 0.00015166666666666668, "loss": 2.0709, "step": 30}, {"epoch": 6.666666666666667, "grad_norm": 2.7161917686462402, "learning_rate": 0.00013500000000000003, "loss": 1.7061, "step": 40}, {"epoch": 8.333333333333334, "grad_norm": 2.772935628890991, "learning_rate": 0.00011833333333333334, "loss": 1.442, "step": 50}, {"epoch": 10.0, "grad_norm": 2.638664960861206, "learning_rate": 0.00010166666666666667, "loss": 1.2446, "step": 60}, {"epoch": 11.666666666666666, "grad_norm": 3.329735517501831, "learning_rate": 8.5e-05, "loss": 1.0977, "step": 70}, {"epoch": 13.333333333333334, "grad_norm": 2.561457395553589, "learning_rate": 6.833333333333333e-05, "loss": 0.9875, "step": 80}, {"epoch": 15.0, "grad_norm": 3.541414976119995, "learning_rate": 5.166666666666667e-05, "loss": 0.8974, "step": 90}], "logging_steps": 10, "max_steps": 120, "num_input_tokens_seen": 0, "num_train_epochs": 20, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1953560505876480.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}