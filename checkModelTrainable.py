# 检查模型能否用于微调的方法
#用以下代码检查是否能顺利加载为 PEFT/Transformers 支持的模型, 如果不支持，则会报错，代表无法进行微调， 否则不报错，可以进行微调
from transformers import AutoModelForCausalLM, AutoTokenizer

model = AutoModelForCausalLM.from_pretrained("D:\\Models-Safetensor\\Qwen2.5-0.5B", trust_remote_code=True)
tokenizer = AutoTokenizer.from_pretrained("D:\\Models-Safetensor\\Qwen2.5-0.5B\\", trust_remote_code=True)

model = AutoModelForCausalLM.from_pretrained("D:\\Models-Safetensor\\MiniMind2", trust_remote_code=True)
tokenizer = AutoTokenizer.from_pretrained("D:\\Models-Safetensor\\MiniMind2\\", trust_remote_code=True)